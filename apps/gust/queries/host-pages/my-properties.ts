import { useMutation, useQuery, UseQueryResult } from '@tanstack/react-query';
import { useCurrentLocale } from '@/lib/i18n/client-translator';
import { API } from '@/lib/axios/axios';
import { getDynamicKey, keys } from '@/lib/react-query/keys';
import { useSearchParams } from 'next/navigation';
import { GetResponse } from '@/types/common';
import { AxiosResponse } from 'axios';
import { Classification, Ratings } from '@/types/homepage';

export const useGetMyPropertiesDetails = ({ pageSize, enabled = true }) => {
  const locale = useCurrentLocale();
  const searchParams = useSearchParams();
  const order_by = searchParams.get('order_by');
  const type = searchParams.get('activeTab') || 'Listed';
  const page = searchParams.get('page') || 1;

  return useQuery({
    queryKey: [...keys.myProperties, page, type, order_by],
    queryFn: () =>
      API('', locale).get(`/v2/host/properties`, {
        params: {
          order_by,
          type,
          page,
          per_page: pageSize,
        },
      }),
    enabled,
  });
};

export const useGetMyPropertiesClassificationDetails = ({
  page,
  type,
  pageSize,
  classificationId,
}: {
  page?: number;
  type: string;
  pageSize?: number;
  classificationId: string | number;
}) => {
  return useQuery({
    queryKey: [...keys.ClassificationDetails, page, type, classificationId],
    queryFn: () =>
      API().get(`/v2/host/properties/${classificationId}/classifications`, {
        params: {
          type,
          page,
          per_page: pageSize,
        },
      }),
  });
};

export const useRejectProperty = (id) => {
  return useMutation({
    mutationFn: async (data: { reason: string }) => {
      return API().post(`/v2/host/properties/management/${id}/reject`, data);
    },
  });
};

export const useDeletePropertyClassification = ({ propertyId, classificationId }) => {
  const url = classificationId
    ? `/v2/host/properties/${propertyId}/${classificationId}`
    : `/v2/host/properties/${propertyId}`;

  return useMutation({
    mutationFn: async (data: { reason: string; other: string | number }) => {
      return API().delete(url, {
        data,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
    },
  });
};

export const useGetDeletionReasons = () => {
  const locale = useCurrentLocale();

  return useQuery({
    queryKey: keys.GetDeletionReasons,
    queryFn: () => API('', locale).get(`/v3/host/reasons/deletion`),
  });
};

export const useRecoverProperty = (id) => {
  return useMutation({
    mutationFn: async () => {
      return API().put(`/v2/host/properties/restore/${id}`);
    },
  });
};

export const usePermanentDeleteProperty = (id) => {
  return useMutation({
    mutationFn: async () => {
      return API().delete(`/v2/host/properties/permanent/${id}`);
    },
  });
};

export const getPropertyHostDetails = async (
  propertyId,
  roomClassificationId
): Promise<AxiosResponse<GetResponse<Classification>>> => {
  const queryPath = `/v2/host/properties/${propertyId}/classifications/${roomClassificationId}`;
  return API().get<GetResponse<Classification>>(queryPath);
};

export const useGetPropertyHostDetails = (
  propertyId: string,
  roomClassificationId: string
): UseQueryResult<AxiosResponse<GetResponse<any> | any>> => {
  return useQuery({
    queryKey: getDynamicKey('propertyHostDetails', [propertyId]),
    queryFn: () => getPropertyHostDetails(propertyId, roomClassificationId),
    enabled: !!propertyId,
    refetchOnMount: 'always',
  });
};

export const getRatings = async (
  page: number,
  propertyId: string,
  roomClassificationId: string
): Promise<AxiosResponse<GetResponse<Ratings>>> => {
  const queryPath = `v2/host/properties/${propertyId}/classifications/${roomClassificationId}/review?&page=${page}&per_page=25`;
  return API().get<GetResponse<Ratings>>(queryPath);
};

export const useGetHostRatings = (
  page: number,
  propertyId: string,
  roomClassificationId: string
): UseQueryResult<AxiosResponse<GetResponse<Ratings>>> => {
  return useQuery({
    queryKey: getDynamicKey('HostRatings', [propertyId]),
    queryFn: () => getRatings(page, propertyId, roomClassificationId),
    placeholderData: (previousData) => previousData,
  });
};

export const getPhotos = async (
  propertyId: string,
  roomClassificationId: string
): Promise<AxiosResponse<GetResponse<any>>> => {
  const queryPath = `v2/host/properties/${propertyId}/classifications/${roomClassificationId}/photos`;
  return API().get<GetResponse<any>>(queryPath);
};

export const useGetPhoto = (
  propertyId: string,
  roomClassificationId: string
): UseQueryResult<AxiosResponse<GetResponse<any>>> => {
  return useQuery({
    queryKey: getDynamicKey('HostPhotos', [propertyId]),
    queryFn: () => getPhotos(propertyId, roomClassificationId),
    placeholderData: (previousData) => previousData,
  });
};

export const useGetAllPropertyClassifications = (propertyId) => {
  const locale = useCurrentLocale();

  return useQuery({
    queryKey: [keys.getAllPropertyClassifications, locale, propertyId],
    queryFn: () => API('', locale).get(`/v2/host/properties/${propertyId}/all-classifications`),
    enabled: !!propertyId,
  });
};

export const useGetHostPropertyDetails = (propertyId: string) => {
  return useQuery({
    queryKey: [keys.getHostPropertyDetails, propertyId],
    queryFn: () => API().get(`/v3/host/properties/${propertyId}`),
    placeholderData: (previousData) => previousData,
  });
};
