'use client';

import { Card } from '@/components/ui/card';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import React, { useEffect, useState } from 'react';
import TabsHeader from './tabs-header';
import { useSearchParams } from 'next/navigation';
import PageHeader from './page-header';
import SetNameWebsite from './setNameToWebsite';
import { useGetThemeSubdomainsProperties } from '@/queries/host-pages/my-rentoor-website';
import useEditWebsiteStore from './store';
import SetNameWebsiteSkeleton from './setNameToWebsite/SetNameWebsiteSkeleton';
import SetUpThemesSkeleton from './setUpThemes/SetUpThemesSkeleton';
import EditSetUpThemes from './setUpThemes';
import ContactInfo from './ContactInfo';
import EmailsSetting from './EmailsSetting';
import { ConflictsManagement } from './ConflictsManagement';
import { useGetHostPropertyDetails } from '@/queries/host-pages/my-properties';

const EditMyRentoorWebsite = ({ id }) => {
  const searchParams = useSearchParams();
  const activeTabFromParams = searchParams.get('activeTab') || 'nameSettings';

  const { activeTab, setActiveTab, setData, setIsLoading } = useEditWebsiteStore();
  const { data: fetchedData, isPending, isLoading: themeLoading, isFetching } = useGetThemeSubdomainsProperties(id);

  // New local state for tab loading
  const [isTabLoading, setIsTabLoading] = useState(false);

  useEffect(() => {
    setActiveTab(activeTabFromParams);
  }, [activeTabFromParams, setActiveTab]);

  // Handle data loading and updates
  useEffect(() => {
    const loading = themeLoading || isFetching || isPending;
    setIsLoading(loading);
    setIsTabLoading(loading);

    if (!loading && fetchedData) {
      setData(fetchedData?.data?.data);
    }
  }, [themeLoading, isFetching, isPending, fetchedData, setIsLoading, setData]);

  // Trigger tab-loading state on tab change
  const handleTabChange = (tabValue) => {
    setIsTabLoading(true);
    setActiveTab(tabValue);

    // Simulate data fetching or processing delay
    setTimeout(() => {
      setIsTabLoading(false);
    }, 500); // Adjust delay based on expected content loading time
  };

  return (
    <div className="host-container min-h-full py-6">
      <PageHeader />
      <Card className="gap-4 p-6">
        <Tabs value={activeTab} className="min-h-full" onValueChange={handleTabChange}>
          <TabsHeader />

          <TabsContent className="bg-transparent" value="nameSettings">
            {isTabLoading ? <SetNameWebsiteSkeleton /> : <SetNameWebsite />}
          </TabsContent>

          <TabsContent className="bg-transparent" value="themeSettings">
            {isTabLoading ? <SetUpThemesSkeleton /> : <EditSetUpThemes />}
          </TabsContent>

          <TabsContent className="bg-transparent" value="contactInfoSettings">
            <ContactInfo />
          </TabsContent>

          <TabsContent className="bg-transparent" value="emailsSetting">
            <EmailsSetting />
          </TabsContent>

          <TabsContent className="bg-transparent" value="conflictsManagement">
            <ConflictsManagement id={id} />
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};

export default EditMyRentoorWebsite;
