import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormProvider, useForm } from 'react-hook-form';
import ZoomImage from '@/components/ui/zoom-image';
import { Separator } from '@/components/ui/separator';
import { FC, useEffect, useMemo, useState } from 'react';
import { useChangeQuery } from '@/components/hostPages/my-properties-page/utils';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { useCreateThemeSubdomains } from '@/queries/host-pages/my-rentoor-website';
import { toast } from 'sonner';
import { AxiosError } from 'axios';
import { useQueryClient } from '@tanstack/react-query';
import { keys } from '@/lib/react-query/keys';
import HeaderComponent from '../header';
import SearchSection from '../search-bar/search-section';
import ColorPickerInput from '../../edit-myrentoor-website/setUpThemes/colorPricker';
import { ImageUpload } from './rhf-upload-file';
import { Button } from '@/components/ui/button';
import { FetchDataThemeType } from '../../create-new-website/types';
import { useGetHostPropertyDetails } from '@/queries/host-pages/my-properties';

type FormData = {
  primary_color: string;
  secondary_color: string;
  logo_en: File | string | null | undefined;
  logo_ar: File | string | null | undefined;
  favicon: string | null | undefined;
  cover_image: string | null | undefined;
};

const themeSchema = (t) =>
  z.object({
    primary_color: z.string(),
    secondary_color: z.string(),
    logo_en: z.union([
      z.instanceof(File, t('themeValidation.required_logo_en')),
      z.string().url(t('themeValidation.invalid_logo_en_url')),
    ]),
    logo_ar: z.any().optional(),
    favicon: z.union([
      z.instanceof(File, t('themeValidation.required_favicon')),
      z.string().url(t('themeValidation.invalid_favicon_url')),
    ]),
    cover_image: z.union([
      z.instanceof(File, t('themeValidation.required_cover_image')),
      z.string().url(t('themeValidation.invalid_cover_image_url')),
    ]),
  });

interface SetUpThemesProps {
  websiteData: FetchDataThemeType | null;
  onToggleLoading?: (isLoading: boolean) => void;
  onMoveToNext?: () => void;
  isEdit?: boolean;
  id: number | string;
}

const SetUpThemes: FC<SetUpThemesProps> = ({
  websiteData,
  onToggleLoading = () => {},
  onMoveToNext = () => {},
  isEdit = false,
  id,
}) => {
  const queryClient = useQueryClient();
  const { paramsWithValues, deleteQueryParam, onChangeQuery } = useChangeQuery();
  const { data: propertyData } = useGetHostPropertyDetails(`${id}`);
  const propertyDetails = propertyData?.data?.data;
  const { mutate, isPending } = useCreateThemeSubdomains(id);
  const t = useScopedI18n('hostPages.myRentoorWebsite');
  const tc = useScopedI18n('common');
  const currentLang = useCurrentLocale();
  const [logoSrc, setLogoSrc] = useState('');

  const initialValues = useMemo(
    () => ({
      primary_color: '#317D53',
      secondary_color: '#56789B',
      logo_en: null,
      logo_ar: null,
      favicon: null,
      cover_image: null,
    }),
    []
  );
  const methods = useForm<FormData>({
    defaultValues: initialValues,
    resolver: zodResolver(themeSchema(t)),
    mode: 'onChange',
  });

  const {
    handleSubmit,
    watch,
    reset,
    formState: { isDirty, dirtyFields },
  } = methods;
  const logo_en = watch('logo_en');
  const logo_ar = watch('logo_ar');

  useEffect(() => {
    const logoFile = currentLang === 'ar' ? (logo_ar ?? logo_en) : (logo_en ?? logo_ar);
    if (logoFile instanceof File) {
      const reader = new FileReader();
      reader.readAsDataURL(logoFile);
      reader.onload = () => setLogoSrc(reader.result as string);
    } else {
      setLogoSrc(logoFile as string);
    }
  }, [logo_en, logo_ar, currentLang]);

  const primary_color = watch('primary_color');
  const secondary_color = watch('secondary_color');

  const cover_image = watch('cover_image') || '';

  const coverSrc = useMemo(() => {
    return typeof cover_image === 'string' ? cover_image : URL.createObjectURL(cover_image);
  }, [cover_image]);

  const onSubmit = async (data: any) => {
    toast.dismiss();
    if (!isDirty && !isEdit) {
      deleteQueryParam('name');
      return onMoveToNext();
    }
    const formData = new FormData();

    formData.append('id', String(id));
    if (websiteData?.primary_color && websiteData?.secondary_color) {
      Object.keys(dirtyFields).forEach((field) => {
        if (data[field]) {
          formData.append(field, data[field]);
        }
      });
    } else {
      ['primary_color', 'secondary_color', 'logo_en', 'favicon', 'cover_image', 'logo_ar'].forEach((key) => {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      });
    }

    onToggleLoading(isPending);

    await mutate(formData, {
      onSuccess: async (response) => {
        if (!isEdit) {
          deleteQueryParam('name');
          onChangeQuery({ activeStep: '2' });
          onMoveToNext();
          toast.success(t('theme_labels.themeSavedSuccess'));
          await queryClient.invalidateQueries({
            queryKey: [...keys.themeProperties, String(response?.data?.data?.property?.id)],
          });
        } else {
          toast.success(t('theme_labels.themeSavedSuccess'));
        }
      },
      onError: (error: AxiosError<{ message: string }>) => {
        toast.error(error?.response?.data?.message ?? tc('something_went_wrong'));
        console.error(error);
      },
    });
  };
  useEffect(() => {
    if (websiteData) {
      reset({
        primary_color: websiteData?.primary_color || '#317D53',
        secondary_color: websiteData?.secondary_color || '#56789B',
        logo_en: websiteData?.domain_photos?.logo_en ?? null,
        logo_ar: websiteData?.domain_photos?.logo_ar ?? null,
        favicon: websiteData?.domain_photos?.favicon ?? null,
        cover_image: websiteData?.domain_photos?.cover ?? null,
      });
    }
  }, [websiteData, reset]);

  useEffect(() => {
    onToggleLoading(isPending);
  }, [isPending]);

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="3xl:flex-row 3xl:gap-10 flex flex-col gap-2 px-2 lg:flex-col">
          <div className="border-gray-75 3xl:min-w-[753px] 3xl:self-center min-h-[30vh] rounded-[48px] border">
            <HeaderComponent secondary_color={secondary_color} logo={logoSrc} />
            <div className="p-4">
              <div className=" sxl:gap-6  relative grid grid-cols-12">
                <div className="sxl:order-1 sxl:col-span-7 order-2 col-span-12">
                  <div className="sxl:flex-col sxl:gap-3 flex h-full flex-col-reverse justify-center">
                    <div className="sxl:-mt-20 sxl:block  flex flex-col-reverse">
                      <div>
                        <h1 className="relative mb-3 line-clamp-1 text-4xl font-bold" style={{ color: primary_color }}>
                          {websiteData?.property?.name || paramsWithValues.name}
                        </h1>

                        <p className="text-justify text-sm leading-6 text-gray-400">{t('descriptionTheme')}</p>
                      </div>

                      <SearchSection
                        className="sxl:absolute sxl:mt-3 sxl:translate-y-0 sxl:max-w-[605px] w-full -translate-y-1/2 transform "
                        primary_color={primary_color}
                        classification_type={propertyDetails?.classification_type}
                      />
                    </div>
                  </div>
                </div>
                <div className="sxl:order-2 sxl:col-span-5 order-1  col-span-12">
                  <div className="relative   w-full overflow-hidden rounded-lg">
                    <ZoomImage
                      className="h-[334px] rounded-lg object-cover"
                      withZoom={false}
                      image={(coverSrc as string) || (websiteData?.domain_photos?.cover as string)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="3xl:w-3/5 flex flex-col gap-1">
            <div className="mt-6 flex justify-between gap-4">
              <ColorPickerInput label={t('theme_labels.primary_color')} name="primary_color" />
              <ColorPickerInput label={t('theme_labels.secondary_color')} name="secondary_color" />
            </div>
            <Separator className="my-4" />
            <div className="flex justify-between">
              <div className="flex flex-col gap-3">
                <p className="text-secondary text-md font-bold">{t('theme_labels.property_logo')}</p>
                <p className="text-gray text-sm">{t('theme_descriptions.width_dimensions')}: 1024px</p>
                <p className="text-gray text-sm">{t('theme_descriptions.hight_dimensions')}: 256px</p>
                <p className="text-gray text-sm">{t('theme_descriptions.size')}: 2MB</p>
                <p className="text-gray text-sm">{t('theme_descriptions.supported_formats')}</p>
              </div>
              <div className="flex gap-4">
                <ImageUpload
                  maxSizeInMB={2}
                  uploadText={tc('attach')}
                  name="logo_en"
                  label={t('theme_labels.logo')}
                  aspectRatio={120 / 40}
                />
                <ImageUpload
                  maxSizeInMB={2}
                  uploadText={tc('attach')}
                  name="logo_ar"
                  label={
                    <span>
                      {t('theme_labels.logo_ar')}{' '}
                      <span className="text-xs text-gray-400">({t('theme_labels.optional')})</span>
                    </span>
                  }
                  aspectRatio={120 / 40}
                />
              </div>
            </div>
            <Separator className="my-4" />
            <div className="flex justify-between">
              <div className="flex flex-col gap-3">
                <p className="text-secondary text-md font-bold">{t('theme_labels.favicon')}</p>
                <p className="text-gray text-sm">{t('theme_descriptions.width_dimensions')}: 32px</p>
                <p className="text-gray text-sm">{t('theme_descriptions.hight_dimensions')}: 32px</p>
                <p className="text-gray text-sm">{t('theme_descriptions.size')}: 2MB</p>
                <p className="text-gray text-sm">{t('theme_descriptions.supported_formats')}</p>
              </div>
              <div className="flex gap-4">
                <ImageUpload maxSizeInMB={2} uploadText={tc('attach')} name="favicon" aspectRatio={32 / 32} />
              </div>
            </div>
            <Separator className="my-4" />
            <div className="flex justify-between">
              <div className="flex flex-col gap-3">
                <p className="text-secondary text-md font-bold">{t('theme_labels.cover_image')}</p>
                <p className="text-gray text-sm">{t('theme_descriptions.width_dimensions')}: 761px</p>
                <p className="text-gray text-sm">{t('theme_descriptions.hight_dimensions')}: 562px</p>
                <p className="text-gray text-sm">{t('theme_descriptions.size')}: 2MB</p>
                <p className="text-gray text-sm">{t('theme_descriptions.supported_formats')}</p>
              </div>
              <div className="flex gap-4">
                <ImageUpload
                  maxSizeInMB={2}
                  uploadText={tc('choose_image')}
                  name="cover_image"
                  aspectRatio={761 / 562}
                />
              </div>
            </div>
          </div>
        </div>
        <Button
          type="submit"
          className="mb-2 mt-8 min-h-12 min-w-40 max-w-max"
          disabled={isPending}
          loading={isPending}>
          {isEdit ? tc('save') : tc('next')}
        </Button>
      </form>
    </FormProvider>
  );
};

export default SetUpThemes;
