'use client';
import React from 'react';
import type { ReactElement, FC } from 'react';
import SearchTriggerButton from './search-trigger-button';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { Button } from '@/components/ui/button';
import IconWrapper from '@/components/icons';
interface SearchSectionProps {
  primary_color?: string;
  className?: string;
  classification_type?: string;
}

const SearchSection: FC<SearchSectionProps> = ({
  primary_color,
  className = '',
  classification_type,
}): ReactElement => {
  const t = useScopedI18n('homepage.search');
  const isMultiClassifications = classification_type === 'different_classifications';

  return (
    <>
      <div
        className={cn(
          'sxl:shadow-3xl shadow-xs bg-white-50 z-10 flex max-h-20  items-center gap-2 ',
          'sxl:h-20  h-12 rounded-full',
          className
        )}>
        <div className="flex flex-1">
          <SearchTriggerButton
            bottomParagraph={t('add_date')}
            icon={<IconWrapper className="gray-storke" name="CalendarIconSmall" size={24} variant="Linear" />}
            topParagraph={t('arrival_date')}
          />

          <Separator className="bg-gray-75" orientation="vertical" />

          <SearchTriggerButton
            bottomParagraph={t('add_date')}
            icon={<IconWrapper className="gray-storke" name="CalendarIconSmall" size={24} variant="Linear" />}
            topParagraph={t('departure_date')}
          />

          <Separator className="bg-gray-75" orientation="vertical" />

          <SearchTriggerButton
            mobileText={t('guests')}
            bottomParagraph={t('add_guests')}
            icon={<IconWrapper name="GuestIcon" size={24} />}
            topParagraph={t('number_guests')}
          />
        </div>
        <div className="sxl:p-2 flex   h-full flex-none items-center justify-center gap-2 p-1 !ps-0">
          <Button
            style={{ background: primary_color }}
            className="sxl:w-[115px] sxl:px-4 sxl:py-3   flex  h-full w-[50px] items-center justify-center gap-2 ltr:rounded-r-full rtl:rounded-l-full"
            type="button">
            {isMultiClassifications ? (
              <>
                <IconWrapper name="SearchIcon" className="text-white-50 sxl:hidden" size={24} variant="TwoTone" />
                <p className="lg-text text-white-50 sxl:flex hidden items-center gap-2 text-center font-bold">
                  <IconWrapper name="SearchIcon" className="text-white-50" size={26} variant="TwoTone" />
                  {t('search')}
                </p>
              </>
            ) : (
              <>
                <IconWrapper name="Tick" className="text-white-50 sxl:hidden" size={24} variant="TwoTone" />
                <p className="lg-text text-white-50 sxl:flex hidden text-center font-bold">{t('book')}</p>
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  );
};

export default SearchSection;
