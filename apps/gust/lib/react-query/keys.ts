export const keys = {
  homepage: ['homepage'],
  properties: ['properties'],
  ratings: ['ratings'],
  searchVars: ['searchVars'],
  priceStats: ['priceStats'],
  areaStats: ['areaStats'],
  availableCountries: ['GetAvailableCountries'],
  userProfile: ['GetUserProfile'],
  propertyDetails: ['GetPropertyDetails'],
  customerCards: ['GetCustomerCards'],
  wallet: ['GetWallet'],
  cities: ['cities'],
  neighborhoods: ['neighborhoods'],
  propertiesCount: ['propertiesCount'],
  wishList: ['wishList'],
  wishListDetails: ['wishListDetails'],
  reservation: ['reservation'],
  dashboardDetails: ['dashboardDetails'],
  myProperties: ['myProperties'],
  cancelReservation: ['cancelReservation'],
  myReservations: ['myReservations'],
  profileDetails: ['profileDetails'],
  getInvoices: ['getInvoices'],
  accountDetails: ['accountDetails'],
  verificationCountries: ['verificationCountries'],
  administrativeAccounts: ['administrativeAccounts'],
  ClassificationDetails: ['ClassificationDetails'],
  GetDeletionReasons: ['GetDeletionReasons'],
  terms: ['terms'],
  policy: ['policy'],
  RecoverProperty: ['RecoverProperty'],
  getClassifications: ['getClassifications'],
  getPhones: ['getPhones'],
  getPropertyTypeOptions: ['getPropertyTypeOptions'],
  getStepData: ['getStepData'],
  faq: ['faq'],
  propertyManagement: ['propertyManagement'],
  propertySearchByGoogleMap: ['propertySearchByGoogleMap'],
  getPropertyDetails: ['getPropertyDetails'],
  getSavedPropertyDetails: ['getSavedPropertyDetails'],
  priceManagement: ['priceManagement'],
  role: ['role'],
  banksDetails: ['banksDetails'],
  payoutsDetails: ['payoutsDetails'],
  getUnitDetails: ['getUnitDetails'],
  propertiesManager: ['propertiesManager'],
  getPools: ['getPools'],
  getAmenities: ['getAmenities'],
  emailOTP: ['emailOTP'],
  phoneOTP: ['phoneOTP'],
  getHostWallet: ['getHostWallet'],
  getConditions: ['getConditions'],
  getAgreement: ['getAgreement'],
  getClassificationsToCopy: ['getClassificationsToCopy'],
  reservationDetails: ['reservationDetails'],
  rejectReservation: ['rejectReservation'],
  propertyHostDetails: ['propertyHostDetails'],
  HostRatings: ['HostRatings'],
  HostPhotos: ['HostPhotos'],
  getAllHostMessages: ['getAllHostMessages'],
  getHostMessages: ['getHostMessages'],
  getHostMessagesPagination: ['getHostMessagesPagination'],
  getAllGuestMessages: ['getAllGuestMessages'],
  getGuestMessages: ['getGuestMessages'],
  getGuestMessagesPagination: ['getGuestMessagesPagination'],
  useSendMessage: ['useSendMessage'],
  useSendImageMessage: ['useSendImageMessage'],
  myRentoorWebsite: ['myRentoorWebsite'],
  myWebsiteProperties: ['myWebsiteProperties'],
  verificationNationalities: ['verificationNationalities'],
  getClassificationUnits: ['getClassificationUnits'],
  getClassificationDetailedDescription: ['getClassificationDetailedDescription'],
  themeProperties: ['themeProperties'],
  getAllPropertyClassifications: ['getAllPropertyClassifications'],
  getHostNotifications: ['getHostNotifications'],
  getGuestNotifications: ['getGuestNotifications'],
  getPropertyInfo: ['getPropertyInfo'],
  getPropertyFacilities: ['getPropertyFacilities'],
  getPropertyLocation: ['getPropertyLocation'],
  getSyncCalenders: ['getSyncCalenders'],
  getPropertyUnits: ['getPropertyUnits'],
  getPropertyPhotos: ['getPropertyPhotos'],
  getMultiUnitsPropertyUnits: ['getPropertyUnits'],
  getPriceByCalender: ['getPriceByCalender'],
  getPropertyAddress: ['getPropertyAddress'],
  invoiceDetails: ['invoiceDetails'],
  shomoosNationalities: ['shomoosNationalities'],
  shomoosIdentityTypes: ['shomoosIdentityTypes'],
  shomoosEscorts: ['shomoosEscorts'],
  shomoosGuestDetails: ['shomoosGuestDetails'],
  getContactInfo: ['getContactInfo'],
  getEmails: ['getEmails'],
  getNotifications: ['getNotifications'],
  getPropertyDetailsPhotos: ['getPropertyDetailsPhotos'],
  getMyRentoorSettings: ['getMyRentoorSettings'],
  getHostPropertyDetails: ['getHostPropertyDetails'],
} as const;

export const getDynamicKey = (key: keyof typeof keys, params: string[]): string[] => {
  const tmp = [...keys[key]] as string[];
  params.forEach((param) => {
    tmp.push(param);
  });
  return tmp;
};
