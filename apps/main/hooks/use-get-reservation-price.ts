import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { type DateRange } from 'react-day-picker';
import { getMessagesFromResponse, parseDateToString } from '@/lib/utils';
import type { PropertyReservationPriceRequest, ReservationDetails } from '@/types/room-details';
import { useGetPropertyReservationPrice } from '@/queries/property-details';
import { useGetQueryParams } from '@/hooks/use-query-params';

const useGetReservationPrice = (
  propertyId: number | string,
  supplyDt: DateRange | undefined,
  units = 0,
  guests = 0,
  isEdit?: boolean,
  isPropertyExpired?: boolean
): [ReservationDetails | null, boolean, boolean] => {
  const [reservation, setReservation] = useState<ReservationDetails | null>(null);
  const [hasError, setHasError] = useState<boolean>(false);
  const getParameter = useGetQueryParams();
  const unitsP = Number(getParameter('units') ?? units);
  const guestsP = Number(getParameter('guests') ?? guests);
  const propertyPrice = useGetPropertyReservationPrice();


  if (isPropertyExpired) {
    setReservation(null);
    return [reservation, hasError, propertyPrice?.isPending];
  }

  useEffect(() => {
    setHasError(false);
    if (supplyDt?.from && supplyDt.to && unitsP > 0 && guestsP > 0 && !isEdit) {
      propertyPrice.mutate(getRequestData(supplyDt, unitsP, guestsP, propertyId), {
        onSuccess: (response: any) => {
          if (response.data.data) setReservation(response.data.data ?? null);
        },
        onError: (e: unknown) => {
          setHasError(true);
          const errors = getMessagesFromResponse(e);
          if (Array.isArray(errors)) {
            errors.forEach((er) => {
              toast.error(er[1][0]);
            });
          } else {
            toast.error(errors);
          }
        },
      });
    }
  }, [unitsP, supplyDt?.from, supplyDt?.to, guestsP, isEdit]);

  return [reservation, hasError, propertyPrice?.isPending];
};

export default useGetReservationPrice;

const getRequestData = (
  date: DateRange | undefined,
  units: number,
  guests: number,
  propertyId: number | string
): PropertyReservationPriceRequest => {
  if (date?.from && date.to)
    return {
      room_classification_id: Number(propertyId),
      number_of_units: units,
      number_of_guests: guests,
      checkin: parseDateToString(date.from),
      checkout: parseDateToString(date.to),
    };
  return {
    checkin: '',
    checkout: '',
    number_of_guests: 0,
    number_of_units: 0,
    room_classification_id: 0,
  };
};
