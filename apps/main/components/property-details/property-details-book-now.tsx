import { type FC, type ReactNode, useEffect, useRef, useState } from 'react';
import type { Classification } from '@/types/homepage';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Separator } from '@/components/ui/separator';
import { Calendar } from '@/components/ui/calendar';
import { InputCounter } from '@/components/ui/input-counter';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import PricePerNight from '@/components/common/price-per-night';
import { useCustomFormatDateByLocale, useCustomFormatTimeStringByLocale } from '@/hooks/use-format-date-by-locale';
import { cn, parseDateToString } from '@/lib/utils';
import useGetCheckinCheckout from '@/hooks/use-get-checkin-checkout';
import useGetReservationPrice from '@/hooks/use-get-reservation-price';
import { useAddQueryParams } from '@/hooks/use-query-params';
import { Skeleton } from '../ui/skeleton';
import styles from './styles.module.css';
import { format, getMonth, getYear, isSameDay } from 'date-fns';
import { useGetCalenderAvailable } from '@/queries/property-details';
import Loader from '../ui/loader';
import { ScrollArea } from '../ui/scroll-area';
import useNavigateWithTenant from '@/hooks/use-navigate-with-tenant';
import { useSearchParams } from 'next/navigation';
import UnitsNumberAlert from '../common/units-number-alert';
import { useGAEvent } from '@/app/[locale]/GoogleAnalytics';
import useHomePageData from '@/hooks/use-home-page-data';

type PropertyDetailsBookNowProps = {
  property: Classification;
  header: ReactNode;
  className?: string;
  inDrawer?: boolean;
  rounded?: boolean;
};
const today = new Date();
const currentYear = String(getYear(today));
const currentMonth = String(getMonth(today) + 1);

const PropertyDetailsBookNow: FC<PropertyDetailsBookNowProps> = ({
  inDrawer = false,
  property,
  header,
  className = '',
  rounded = true,
}) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [date, setDate] = useGetCheckinCheckout(property);
  const searchParams = useSearchParams();
  const guestsParam = searchParams.get('guests');
  const unitsParam = searchParams.get('units');
  const comT = useScopedI18n('common') as (key: string, options?: object) => string;

  const [guests, setGuests] = useState<number>(Number(guestsParam || 1));
  const [units, setUnits] = useState<number>(Number(unitsParam || 1));
  const [monthOffset, setMonthOffset] = useState(3);
  const [loadingMore, setLoadingMore] = useState(false);
  const [disabledDaysData, setDisabledDaysData] = useState<any[]>([]);
  const t = useScopedI18n('property_details');
  const hst = useScopedI18n('homepage.search');
  const rT = useScopedI18n('reserve');
  const formatByLocal = useCustomFormatDateByLocale();
  const formatTimeStringByLocal = useCustomFormatTimeStringByLocale();
  const { navigateTo } = useNavigateWithTenant();
  const addQueryParams = useAddQueryParams();
  const { sendGAEvent } = useGAEvent();
  const [currentMonthUpdate, setCurrentMonth] = useState(currentMonth);

  const { data: homePageData } = useHomePageData();
  const isPropertyExpired = homePageData?.isPropertyExpired;

  const [reservation, hasError, isPending] = useGetReservationPrice(
    property.id,
    date,
    units,
    guests,
    isPropertyExpired
  );
  const { data, isPending: isGetCalendarAvaliablePending } = useGetCalenderAvailable(
    `${property.id}`,
    currentYear,
    currentMonthUpdate
  );

  useEffect(() => {
    if (data && data.data.data.length) {
      setDisabledDaysData((prev) => [...prev, ...data.data.data]);
    }
  }, [data?.data.data]);

  const parseStringToDate = (dateString: string) => new Date(dateString);

  const disabledDays = disabledDaysData
    .filter((item) => item.status === 'Not available')
    .map((item) => parseStringToDate(item.date).toISOString());

  const handleBookNowBtn = (): void => {
    sendGAEvent({ action: 'reserve_again_clicked' });

    if (units >= 1 && guests >= 1 && date?.from && date.to) {
      const qParams = addQueryParams([
        { name: 'units', value: units },
        { name: 'guests', value: guests },
        { name: 'checkin', value: parseDateToString(date.from) },
        { name: 'checkout', value: parseDateToString(date.to) },
        { name: 'booking_type', value: property.booking_type },
      ]);
      navigateTo(`/room/${property.id}/reserve?${qParams}`);
    }
  };

  useEffect(() => {
    if (guestsParam) {
      setGuests(Number(guestsParam));
    }
    if (unitsParam) {
      setUnits(Number(unitsParam));
    }
  }, [guestsParam, unitsParam]);

  const handleLoadMoreMonths = () => {
    setLoadingMore(true);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setMonthOffset((prev) => prev + 3);
      setLoadingMore(false);
      setCurrentMonth((prev) => String(Number(prev) + 3));
      clearTimeout(timeoutRef.current!);
      timeoutRef.current = null;
    }, 1000);
  };

  useEffect(() => {
    if (isPropertyExpired) {
      setDate(undefined);
    }
    

    if (date?.from && date?.to && isSameDay(date.from, date.to)) {
      setDate((prev) => {
        if (prev?.to === undefined) return prev;
        return { from: prev.from, to: undefined };
      });
    }
  }, [date?.from, date?.to]);

  if (!disabledDaysData && isGetCalendarAvaliablePending && isPending) {
    return <Loader size={'[5px]'} />;
  }

  return (
    <div
      className={cn(
        `bg-white-50 shadow-reserve flex flex-col  overflow-hidden  pt-6`,
        inDrawer ? 'max-h-[90vh]' : '',
        rounded ? 'rounded-lg' : '',
        className
      )}>
      {header}
      <div className={`scrollable-content  flex flex-1 flex-col overflow-auto py-3`}>
        <p className="md-text mb-3 px-4 text-gray-400 ">{t('update_dates')}</p>
        <Collapsible>
          <CollapsibleTrigger className="justify-between px-4 ">
            <div className="flex flex-col items-start gap-2">
              <p className={cn('md-text font-bold', !date?.from ? 'text-primary' : 'text-secondary')}>
                {hst('arrival_date')}
              </p>
              {date?.from ? (
                <>
                  <p className="sm-text text-gray-400">{formatByLocal(date.from)}</p>
                  <p className="sm-text text-gray-400">{formatTimeStringByLocal(property?.checkin_time)}</p>
                </>
              ) : (
                <p className="sm-text text-gray-400">{hst('enter_arrival_date')}</p>
              )}
            </div>
            <Separator orientation="vertical" />
            <div className="flex flex-col items-start gap-2">
              <p
                className={cn(
                  'md-text font-bold',
                  !date?.from ? 'text-secondary' : date?.from && !date?.to ? 'text-primary' : 'text-secondary'
                )}>
                {hst('departure_date')}
              </p>{' '}
              {date?.to ? (
                <>
                  <p className="sm-text text-gray-400">{formatByLocal(date.to)}</p>
                  <p className="sm-text text-gray-400">{formatTimeStringByLocal(property?.checkout_time)}</p>
                </>
              ) : (
                <p className="sm-text text-gray-400">{hst('enter_departure_date')}</p>
              )}
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent className="px-4">
            <Separator orientation="horizontal" className="my-2" />
            <ScrollArea className="h-[19rem]">
              {
                <>
                  <Calendar
                    disabled={(day: Date) => {
                      const normalizedDay = format(day, 'yyyy-MM-dd');
                      return disabledDays?.some(
                        (disabledDate) => format(new Date(disabledDate), 'yyyy-MM-dd') === normalizedDay
                      );
                    }}
                    mode="range"
                    onSelect={(range) => {
                      const selected = range?.from;

                      if (!date?.from) {
                        setDate({ from: selected, to: undefined });
                      } else {
                        setDate(range);
                      }
                    }}
                    selected={date}
                    disableNavigation
                    numberOfMonths={monthOffset}
                  />
                </>
              }
              <div className="m-auto flex items-center justify-center">
                <Button variant={'link'} onClick={handleLoadMoreMonths} className="mt-2 text-sm" disabled={loadingMore}>
                  {loadingMore || isPending ? (
                    <>
                      <Loader size={'[5px]'} />
                      {t('loading_more')}
                    </>
                  ) : (
                    t('load_more_months')
                  )}
                </Button>
              </div>
            </ScrollArea>
          </CollapsibleContent>
        </Collapsible>
        <Separator orientation="horizontal" className="mb-3 mt-2" />
        <div className="flex flex-col gap-3 px-4">
          <div className="flex items-center justify-between">
            <p className="md-text text-secondary font-bold">{hst('number_guests')}</p>
            <InputCounter
              min={1}
              max={property.accommodates}
              setValue={setGuests}
              value={guests}
              onChange={(event) => {
                const v = Number(event.target.value);
                if (v > 1 && v <= property.accommodates) setGuests(Number(event.target.value));
              }}
            />
          </div>
          <Separator orientation="horizontal" />
          <div className="flex items-center justify-between">
            <p className="md-text text-secondary font-bold">{t('units')}</p>
            <Select
              value={String(units)}
              onValueChange={(value) => {
                setUnits(Number(value));
              }}>
              <SelectTrigger className="w-40 text-center">
                <SelectValue placeholder={t('units')} />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  {Array.from({ length: property.number_of_unist_available }, (value, index) => index + 1).map(
                    (value) => {
                      return (
                        <SelectItem key={value} value={String(value)}>
                          {value}
                        </SelectItem>
                      );
                    }
                  )}
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          {units > Number(reservation?.price.number_of_units_available) && (
            <UnitsNumberAlert
              label={comT('available_units', {
                count: Number(reservation?.price.number_of_units_available),
              })}
            />
          )}
        </div>
      </div>
      <div
        className={cn(
          'shadow-mobile bg-white-50 flex w-full items-center  justify-between border-t-[0.3px]  border-t-gray-50',
          inDrawer ? '' : 'fixed bottom-0 left-0 z-10 w-full md:relative'
        )}>
        <Button
          onClick={handleBookNowBtn}
          disabled={hasError || isPending || isPropertyExpired}
          className={`${styles.bookBtn}`}>
          {property?.booking_type === 'request_to_book' ? rT('request_booking') : hst('book')}
        </Button>
        <div className="grow px-1">
          <div className="flex justify-center">
            {isPending && !hasError ? (
              <div className="flex  flex-1 flex-col gap-2">
                <Skeleton className="h-4 w-full bg-gray-200" />
                <Skeleton className="mt-1 h-4 w-2/3 bg-gray-200" />
              </div>
            ) : hasError ? (
              t('unavailable')
            ) : (
              reservation && <PricePerNight nightPrice={reservation.price} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
export default PropertyDetailsBookNow;
